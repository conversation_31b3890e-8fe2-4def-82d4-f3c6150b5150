import { Router, Request, Response } from 'express';
import { storage } from '../storage';
import { z } from 'zod';
import { validateEmailDomain } from '../../client/src/lib/email-validator';
import { getPaymentConfig } from '../config-storage';
import { nanoid } from 'nanoid';

export const customCheckoutRouter = Router();

// Schema for creating/updating custom checkout pages
const customCheckoutPageSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  slug: z.string().optional(),
  productName: z.string().min(1, 'Product name is required'),
  productDescription: z.string().min(1, 'Product description is required'),
  price: z.number().positive('Price must be positive'),
  imageUrl: z.union([z.string().url(), z.string().length(0)]).optional(),
  paymentMethod: z.enum(['paypal', 'custom-link', 'paypal-button-embed', 'trial-custom-link', 'trial-paypal-button-embed']),
  customPaymentLinkId: z.string().optional(),
  paypalButtonId: z.string().optional(),
  trialCustomPaymentLinkId: z.string().optional(),
  trialPaypalButtonId: z.string().optional(),
  confirmationMessage: z.string().optional(),
  headerTitle: z.string().optional(),
  footerText: z.string().optional(),
  headerLogo: z.string().optional(),
  footerLogo: z.string().optional(),
  themeMode: z.enum(['light', 'dark']).default('light'),
  useReferrerMasking: z.boolean().default(false),
  redirectDelay: z.number().min(0).max(10000).default(2000),
  smtpProviderId: z.string().optional(),
  requireAllowedEmail: z.boolean().default(false),
  isTrialCheckout: z.boolean().default(false),
  expiresAt: z.string().optional(),
  active: z.boolean().default(true)
});

// Simple admin check middleware
const checkAdmin = (req: Request, res: Response, next: Function) => {
  console.log('Checking admin session:', req.session);
  if (req.session && req.session.isAdmin) {
    console.log('Admin session verified:', req.session.isAdmin);
    next();
  } else {
    console.log('Admin session verification failed');
    res.status(401).json({ message: 'Unauthorized' });
  }
};

// Get all custom checkout pages (admin only)
customCheckoutRouter.get('/', checkAdmin, async (req: Request, res: Response) => {
  try {
    const pages = await storage.getCustomCheckoutPages();
    res.json(pages);
  } catch (error) {
    console.error('Error fetching custom checkout pages:', error);
    res.status(500).json({ message: 'Failed to fetch custom checkout pages' });
  }
});

// Get a specific custom checkout page by ID (admin only)
customCheckoutRouter.get('/:id', checkAdmin, async (req: Request, res: Response) => {
  try {
    const id = parseInt(req.params.id);
    const page = await storage.getCustomCheckoutPage(id);

    if (!page) {
      return res.status(404).json({ message: 'Custom checkout page not found' });
    }

    console.log('GET checkout page data being returned:', JSON.stringify(page, null, 2));
    res.json(page);
  } catch (error) {
    console.error('Error fetching custom checkout page:', error);
    res.status(500).json({ message: 'Failed to fetch custom checkout page' });
  }
});

// Create a new custom checkout page (admin only)
customCheckoutRouter.post('/', checkAdmin, async (req: Request, res: Response) => {
  try {
    const validatedData = customCheckoutPageSchema.parse(req.body);

    // Generate a slug if not provided
    if (!validatedData.slug) {
      validatedData.slug = `${validatedData.title.toLowerCase().replace(/[^a-z0-9]+/g, '-')}-${nanoid(6)}`;
    }

    // Check if the slug is already in use
    const existingPage = await storage.getCustomCheckoutPageBySlug(validatedData.slug);
    if (existingPage) {
      return res.status(400).json({ message: 'Slug is already in use' });
    }

    // Get payment config
    const paymentConfig = getPaymentConfig();

    // If using custom-link, validate the link ID
    if (validatedData.paymentMethod === 'custom-link' && validatedData.customPaymentLinkId) {
      const customLinkProvider = paymentConfig.providers.find(p => p.id === 'custom-link');
      if (customLinkProvider && customLinkProvider.config) {
        const config = customLinkProvider.config as any;
        if (Array.isArray(config.links)) {
          const linkExists = config.links.some((link: any) => link.id === validatedData.customPaymentLinkId);
          if (!linkExists) {
            return res.status(400).json({ message: 'Selected custom payment link does not exist' });
          }
        }
      }
    }

    // If using paypal-button-embed, validate the button ID
    if (validatedData.paymentMethod === 'paypal-button-embed' && validatedData.paypalButtonId) {
      const paypalButtonEmbedProvider = paymentConfig.providers.find(p => p.id === 'paypal-button-embed');
      if (paypalButtonEmbedProvider && paypalButtonEmbedProvider.config) {
        const config = paypalButtonEmbedProvider.config as any;
        if (Array.isArray(config.buttons)) {
          const buttonExists = config.buttons.some((button: any) => button.id === validatedData.paypalButtonId);
          if (!buttonExists) {
            return res.status(400).json({ message: 'Selected PayPal button does not exist' });
          }
        }
      }
    }

    // Create the page
    const now = new Date().toISOString();
    const page = await storage.createCustomCheckoutPage({
      ...validatedData,
      createdAt: now,
      updatedAt: now
    });

    res.status(201).json(page);
  } catch (error) {
    console.error('Error creating custom checkout page:', error);

    if (error instanceof z.ZodError) {
      return res.status(400).json({
        message: 'Validation error',
        errors: error.errors
      });
    }

    res.status(500).json({ message: 'Failed to create custom checkout page' });
  }
});

// Update a custom checkout page (admin only)
customCheckoutRouter.put('/:id', checkAdmin, async (req: Request, res: Response) => {
  try {
    const id = parseInt(req.params.id);
    const page = await storage.getCustomCheckoutPage(id);

    if (!page) {
      return res.status(404).json({ message: 'Custom checkout page not found' });
    }

    console.log('❌ WRONG FILE - Raw request data:', JSON.stringify(req.body, null, 2));
    const validatedData = customCheckoutPageSchema.parse(req.body);
    console.log('❌ WRONG FILE - Validated data after parsing:', JSON.stringify(validatedData, null, 2));

    // Check if slug is being changed and if it's already in use
    if (validatedData.slug && validatedData.slug !== page.slug) {
      const existingPage = await storage.getCustomCheckoutPageBySlug(validatedData.slug);
      if (existingPage && existingPage.id !== id) {
        return res.status(400).json({ message: 'Slug is already in use' });
      }
    }

    // Update the page
    console.log('Updating page with validated data:', JSON.stringify(validatedData, null, 2));
    const updatedPage = await storage.updateCustomCheckoutPage(id, {
      ...validatedData,
      updatedAt: new Date().toISOString()
    });

    console.log('Updated page result:', JSON.stringify(updatedPage, null, 2));
    res.json(updatedPage);
  } catch (error) {
    console.error('Error updating custom checkout page:', error);

    if (error instanceof z.ZodError) {
      return res.status(400).json({
        message: 'Validation error',
        errors: error.errors
      });
    }

    res.status(500).json({ message: 'Failed to update custom checkout page' });
  }
});

// Delete a custom checkout page (admin only)
customCheckoutRouter.delete('/:id', checkAdmin, async (req: Request, res: Response) => {
  try {
    const id = parseInt(req.params.id);
    const page = await storage.getCustomCheckoutPage(id);

    if (!page) {
      return res.status(404).json({ message: 'Custom checkout page not found' });
    }

    await storage.deleteCustomCheckoutPage(id);

    res.json({ message: 'Custom checkout page deleted successfully' });
  } catch (error) {
    console.error('Error deleting custom checkout page:', error);
    res.status(500).json({ message: 'Failed to delete custom checkout page' });
  }
});

// Public route to get a custom checkout page by slug
customCheckoutRouter.get('/public/:slug', async (req: Request, res: Response) => {
  try {
    const { slug } = req.params;
    const page = await storage.getCustomCheckoutPageBySlug(slug);

    if (!page) {
      return res.status(404).json({ message: 'Custom checkout page not found' });
    }

    if (!page.active) {
      return res.status(404).json({ message: 'This checkout page is no longer active' });
    }

    // Check if the page has expired
    if (page.expiresAt) {
      const expiryDate = new Date(page.expiresAt);
      if (expiryDate < new Date()) {
        return res.status(404).json({ message: 'This checkout page has expired' });
      }
    }



    // Increment view count
    await storage.incrementCustomCheckoutPageViews(page.id);

    // Return the page without sensitive information
    const { id, title, productName, productDescription, price, imageUrl, paymentMethod, requireAllowedEmail, isTrialCheckout, confirmationMessage, headerTitle, footerText } = page;
    const publicData = { id, title, productName, productDescription, price, imageUrl, paymentMethod, requireAllowedEmail, isTrialCheckout, confirmationMessage, headerTitle, footerText };
    console.log('Public checkout page data being returned:', JSON.stringify(publicData, null, 2));
    res.json(publicData);
  } catch (error) {
    console.error('Error fetching public custom checkout page:', error);
    res.status(500).json({ message: 'Failed to fetch checkout page' });
  }
});

// Process checkout for a custom checkout page
customCheckoutRouter.post('/checkout/:slug', async (req: Request, res: Response) => {
  try {
    const { slug } = req.params;
    const { fullName, email, country, appType, macAddress } = req.body;

    // Validate input
    if (!fullName || !email) {
      return res.status(400).json({ message: 'Full name and email are required' });
    }

    // Validate country
    if (!country) {
      return res.status(400).json({ message: 'Country is required' });
    }

    // Validate MAC address if a device type that requires it is selected
    const requiresMac = ['MAG', 'Formuler Z', 'Smart STB', 'STBEMU'].includes(appType || '');
    if (requiresMac && !macAddress) {
      return res.status(400).json({ message: 'MAC address is required for this device type' });
    }

    // Validate email domain
    const emailValidation = validateEmailDomain(email);
    if (!emailValidation.isValid) {
      return res.status(400).json({ message: emailValidation.message });
    }

    // Get the custom checkout page
    const page = await storage.getCustomCheckoutPageBySlug(slug);

    if (!page) {
      return res.status(404).json({ message: 'Custom checkout page not found' });
    }

    if (!page.active) {
      return res.status(404).json({ message: 'This checkout page is no longer active' });
    }

    // Check if the page has expired
    if (page.expiresAt) {
      const expiryDate = new Date(page.expiresAt);
      if (expiryDate < new Date()) {
        return res.status(404).json({ message: 'This checkout page has expired' });
      }
    }



    // Validate email against allowed list if required
    // Trial checkouts NEVER block emails, regular checkouts only block if requireAllowedEmail is true
    if (!page.isTrialCheckout && page.requireAllowedEmail) {
      // Check if email is in the allowed list
      const isAllowed = await storage.isEmailAllowed(email);
      if (!isAllowed) {
        return res.status(400).json({ message: 'The email you entered is not in our database. This checkout page is only for existing subscribers.' });
      }
    }

    // Create checkout data
    const checkoutData = {
      fullName,
      email,
      productId: 'custom',
      quantity: 1,
      customProductName: page.productName,
      customProductDescription: page.productDescription,
      customProductPrice: page.price,
      customCheckoutPageId: page.id,
      country,
      appType: appType || undefined,
      macAddress: macAddress || undefined
    };

    // Get product data
    const product = {
      id: 'custom',
      name: page.productName,
      description: page.productDescription,
      price: page.price,
      imageUrl: page.imageUrl || ''
    };

    // Get payment config
    const paymentConfig = getPaymentConfig();
    let paymentMethod = page.paymentMethod;
    let paymentProvider;
    let invoiceResult;

    // Determine payment provider based on payment method
    if (paymentMethod === 'paypal') {
      paymentProvider = paymentConfig.providers.find(p => p.id === 'paypal');
    } else if (paymentMethod === 'custom-link' || paymentMethod === 'trial-custom-link') {
      paymentProvider = paymentConfig.providers.find(p => p.id === 'custom-link');

      // If a specific custom payment link is specified, use it
      if (page.customPaymentLinkId && paymentProvider && paymentProvider.config) {
        const config = paymentProvider.config as any;
        if (Array.isArray(config.links)) {
          // Find the specified link and make it the only active one for this checkout
          const linkIndex = config.links.findIndex((link: any) => link.id === page.customPaymentLinkId);
          if (linkIndex !== -1) {
            // Create a temporary copy of the config for this checkout
            const tempConfig = { ...config };
            tempConfig.links = [...config.links];

            // Make only the specified link active
            tempConfig.links.forEach((link: any, idx: number) => {
              link.active = idx === linkIndex;
            });

            // Use the temporary config for this checkout
            paymentProvider = {
              ...paymentProvider,
              config: tempConfig
            };
          }
        }
      }

      // If a specific trial custom payment link is specified, use it
      if (page.trialCustomPaymentLinkId && paymentProvider && paymentProvider.config && page.paymentMethod === 'trial-custom-link') {
        const config = paymentProvider.config as any;
        if (Array.isArray(config.links)) {
          // Find the specified link and make it the only active one for this checkout
          const linkIndex = config.links.findIndex((link: any) => link.id === page.trialCustomPaymentLinkId);
          if (linkIndex !== -1) {
            // Create a temporary copy of the config for this checkout
            const tempConfig = { ...config };
            tempConfig.links = [...config.links];

            // Make only the specified link active
            tempConfig.links.forEach((link: any, idx: number) => {
              link.active = idx === linkIndex;
            });

            // Use the temporary config for this checkout
            paymentProvider = {
              ...paymentProvider,
              config: tempConfig
            };
          }
        }
      }
    } else if (paymentMethod === 'paypal-button-embed' || paymentMethod === 'trial-paypal-button-embed') {
      paymentProvider = paymentConfig.providers.find(p => p.id === 'paypal-button-embed');

      // If a specific PayPal button is specified, use it
      if (page.paypalButtonId && paymentProvider && paymentProvider.config) {
        const config = paymentProvider.config as any;
        if (Array.isArray(config.buttons)) {
          // Find the specified button and make it the only active one for this checkout
          const buttonIndex = config.buttons.findIndex((button: any) => button.id === page.paypalButtonId);
          if (buttonIndex !== -1) {
            // Create a temporary copy of the config for this checkout
            const tempConfig = { ...config };
            tempConfig.buttons = [...config.buttons];

            // Make only the specified button active
            tempConfig.buttons.forEach((button: any, idx: number) => {
              button.active = idx === buttonIndex;
            });

            // Use the temporary config for this checkout
            paymentProvider = {
              ...paymentProvider,
              config: tempConfig
            };
          }
        }
      }

      // If a specific trial PayPal button is specified, use it
      if (page.trialPaypalButtonId && paymentProvider && paymentProvider.config && page.paymentMethod === 'trial-paypal-button-embed') {
        const config = paymentProvider.config as any;
        if (Array.isArray(config.buttons)) {
          // Find the specified button and make it the only active one for this checkout
          const buttonIndex = config.buttons.findIndex((button: any) => button.id === page.trialPaypalButtonId);
          if (buttonIndex !== -1) {
            // Create a temporary copy of the config for this checkout
            const tempConfig = { ...config };
            tempConfig.buttons = [...config.buttons];

            // Make only the specified button active
            tempConfig.buttons.forEach((button: any, idx: number) => {
              button.active = idx === buttonIndex;
            });

            // Use the temporary config for this checkout
            paymentProvider = {
              ...paymentProvider,
              config: tempConfig
            };
          }
        }
      }
    }

    if (!paymentProvider) {
      return res.status(500).json({
        message: 'No active payment provider configured'
      });
    }

    // Process payment based on payment method
    if (paymentMethod === 'paypal') {
      // Generate PayPal invoice using the Invoicing API
      const { createPayPalInvoice } = await import('../services/paypal-invoice');
      invoiceResult = await createPayPalInvoice(checkoutData, product);
    } else if (paymentMethod === 'custom-link' || paymentMethod === 'trial-custom-link') {
      // Generate custom payment link
      const { createCustomPaymentLink } = await import('../services/custom-link');
      invoiceResult = await createCustomPaymentLink(checkoutData, product);
    } else if (paymentMethod === 'paypal-button-embed' || paymentMethod === 'trial-paypal-button-embed') {
      // Generate PayPal button embed
      const { createPayPalButtonEmbed } = await import('../services/paypal-button-embed');
      invoiceResult = await createPayPalButtonEmbed(checkoutData, product);
    } else {
      return res.status(500).json({
        message: `Payment method ${paymentMethod} is not supported`
      });
    }

    // Extract invoice result
    const { id: paypalInvoiceId, url: paypalInvoiceUrl, isSimulated, isDraft, noPayPalAccount, status, error } = invoiceResult;

    // Determine the invoice status
    let invoiceStatus = "sent";
    let invoiceNotes;

    if (noPayPalAccount) {
      invoiceStatus = "no_paypal";
      invoiceNotes = `No PayPal invoice generated because the customer email is not valid.`;
    } else if (isSimulated) {
      invoiceStatus = "simulated";
      invoiceNotes = `Simulated invoice due to API error: ${error}`;
    } else if (isDraft) {
      invoiceStatus = "draft";
      invoiceNotes = `Invoice created in draft status. Status: ${status}`;
    }

    // Create invoice in our storage
    const invoice = await storage.createInvoice({
      customerName: checkoutData.fullName,
      customerEmail: checkoutData.email,
      productId: product.id,
      amount: product.price,
      status: invoiceStatus,
      paypalInvoiceId,
      paypalInvoiceUrl,
      isTrialOrder: page.isTrialCheckout || false,
      hasUpgraded: false,
      createdAt: new Date().toISOString(),
      notes: invoiceNotes,
      customCheckoutPageId: page.id,
      country: checkoutData.country,
      appType: checkoutData.appType,
      macAddress: checkoutData.macAddress
    });

    // Increment conversion count
    await storage.incrementCustomCheckoutPageConversions(page.id);

    // Handle email notifications based on checkout type and payment method
    if (paymentMethod === 'paypal' && noPayPalAccount) {
      // Don't send email for PayPal if the customer doesn't have a PayPal account
      console.log(`No email notification sent for invoice ID: ${invoice.id} (customer has no PayPal account)`);
    } else {
      try {
        const { sendInvoiceEmail } = await import('../services/email');

        // For trial checkout pages with PayPal Button Embed, don't send email (button will be shown on page)
        if (page.isTrialCheckout && (paymentMethod === 'paypal-button-embed' || paymentMethod === 'trial-paypal-button-embed')) {
          console.log(`No email sent for trial checkout ID: ${invoice.id} - payment button will be shown on page`);
        }
        // For regular checkout pages, always send email
        else if (!page.isTrialCheckout) {
          // For PayPal Button Embed, pass the entire invoiceResult object
          if (paymentMethod === 'paypal-button-embed') {
            await sendInvoiceEmail(
              checkoutData,
              product,
              invoiceResult,
              paymentMethod,
              page.smtpProviderId
            );
          } else {
            await sendInvoiceEmail(
              checkoutData,
              product,
              paypalInvoiceUrl,
              paymentMethod,
              page.smtpProviderId
            );
          }
          console.log(`Email notification sent for invoice ID: ${invoice.id} using ${paymentMethod} and SMTP provider: ${page.smtpProviderId || 'default'}`);
        }
      } catch (emailError) {
        console.warn("Email notification could not be sent:", emailError);
        // We don't want to fail the checkout if just the email fails
      }
    }

    // Return success response
    res.status(201).json({
      invoiceId: invoice.id,
      paypalInvoiceId,
      paypalInvoiceUrl,
      isSimulated: isSimulated || false,
      isDraft: isDraft || false,
      noPayPalAccount: noPayPalAccount || false,
      status: status || 'SENT',
      message: paymentMethod === 'custom-link'
        ? `Order processed successfully. A payment link has been sent to your email.`
        : paymentMethod === 'paypal-button-embed'
          ? `Order processed successfully. A PayPal payment button has been sent to your email.`
          : noPayPalAccount
            ? `Order processed successfully. No PayPal invoice was generated for this email.`
            : isSimulated
              ? `A simulated invoice was created due to an error with the PayPal Invoicing API: ${error}`
              : isDraft
                ? `Invoice created successfully in draft status. You can view and send it from your PayPal account.`
                : 'Invoice created and sent successfully'
    });
  } catch (error) {
    console.error("Error processing custom checkout:", error);

    if (error instanceof z.ZodError) {
      return res.status(400).json({
        message: "Validation error",
        errors: error.errors
      });
    }

    res.status(500).json({
      message: "Failed to process checkout",
      error: error instanceof Error ? error.message : "Unknown error"
    });
  }
});

// Export the router
export default customCheckoutRouter;
