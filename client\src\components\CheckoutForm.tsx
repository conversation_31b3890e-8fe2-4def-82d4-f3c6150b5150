import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useMutation } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import { ArrowLeft, ArrowRight, CircleCheck, AlertCircle } from 'lucide-react';
import { validateEmailDomain } from '@/lib/email-validator';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { useToast } from '@/hooks/use-toast';
import { useErrorDialog } from '@/hooks/use-error-dialog';
import { useConfirmationDialog } from '@/hooks/use-confirmation-dialog';
import OrderSummary from './OrderSummary';
import { CheckoutFormProps, CheckoutState, CheckoutFormData } from '@/lib/types';

// Validation schema
const formSchema = z.object({
  fullName: z.string().min(2, "Full name is required"),
  email: z.string()
    .email("Please enter a valid email address")
    .refine(
      (email) => validateEmailDomain(email).isValid,
      (email) => ({
        message: validateEmailDomain(email).message || "Email domain not allowed"
      })
    ),
});

const CheckoutForm: React.FC<CheckoutFormProps> = ({ product, onBack }) => {
  const [checkoutState, setCheckoutState] = useState<CheckoutState>({ status: 'form' });
  const { toast } = useToast();
  const { showError } = useErrorDialog();
  const { showConfirmation } = useConfirmationDialog();

  // Form setup
  const form = useForm<CheckoutFormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      fullName: '',
      email: '',
    },
  });

  // Checkout mutation
  const { mutate, isPending } = useMutation({
    mutationFn: async (data: CheckoutFormData & { productId: number }) => {
      // apiRequest already parses the JSON response, so we don't need to call .json() again
      return apiRequest('/api/checkout', 'POST', data);
    },
    onSuccess: (data) => {
      console.log('Checkout successful:', data);
      setCheckoutState({
        status: 'success',
        paypalInvoiceUrl: data.paypalInvoiceUrl
      });
    },
    onError: (error) => {
      console.error('Checkout error:', error);
      setCheckoutState({
        status: 'error',
        error: error instanceof Error ? error.message : 'Failed to process checkout'
      });
      showError(
        "Checkout Error",
        <p>{error instanceof Error ? error.message : 'Failed to process checkout'}</p>
      );
    }
  });

  // Submit handler
  const onSubmit = (data: CheckoutFormData) => {
    // Check if product exists and has an id before proceeding
    if (!product || typeof product.id === 'undefined') {
      setCheckoutState({
        status: 'error',
        error: 'Product information is missing. Please try again or contact support.'
      });
      showError(
        "Checkout Error",
        <p>Product information is missing. Please try again or contact support.</p>
      );
      return;
    }

    // Show confirmation dialog before proceeding with purchase
    showConfirmation(
      "Confirm Your Purchase",
      <div className="space-y-3">
        <p>You are about to purchase:</p>
        <p className="font-medium">{product.name}</p>
        <p>Price: ${parseFloat(product.price.toString()).toFixed(2)}</p>
        <p className="text-sm text-muted-foreground mt-2">
          By clicking confirm, you agree to our terms and conditions.
        </p>
      </div>,
      () => {
        // This function runs when the user confirms
        setCheckoutState({ status: 'processing' });
        mutate({
          ...data,
          productId: product.id
        });
      },
      {
        confirmText: "Proceed with Purchase",
        cancelText: "Cancel"
      }
    );
  };

  // Handler to try again after error
  const handleTryAgain = () => {
    setCheckoutState({ status: 'form' });
  };

  // Price calculation
  const price = parseFloat(product.price.toString()).toFixed(2);

  // Render checkout form or status screens
  const renderContent = () => {
    if (checkoutState.status === 'form') {
      return (
        <div>
          <OrderSummary product={product} />

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)}>
              <div className="mb-6">
                <h3 className="text-lg font-semibold mb-4">Customer Information</h3>
                <div className="grid grid-cols-1 gap-4">
                  <FormField
                    control={form.control}
                    name="fullName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Full Name</FormLabel>
                        <FormControl>
                          <Input placeholder="John Smith" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Email Address</FormLabel>
                        <FormControl>
                          <Input type="email" placeholder="Your Email" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              <div className="border-t border-border pt-6 mb-6">
                <div className="flex justify-between mb-2">
                  <span>Subtotal</span>
                  <span>${price}</span>
                </div>
                <div className="flex justify-between font-bold">
                  <span>Total</span>
                  <span>${price}</span>
                </div>
              </div>

              <div className="text-center">
                <Button
                  type="submit"
                  className="w-full bg-primary hover:bg-primary/90 text-primary-foreground py-6"
                  disabled={isPending}
                >
                  <span>Generate PayPal Invoice</span>
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
                <p className="text-sm text-muted-foreground mt-2">
                  You'll receive an invoice via email to complete your purchase securely through PayPal.
                </p>
              </div>
            </form>
          </Form>
        </div>
      );
    } else if (checkoutState.status === 'processing') {
      return (
        <div className="h-full flex flex-col items-center justify-center p-6">
          <div className="mb-6">
            <svg className="animate-spin h-12 w-12 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          </div>
          <h3 className="text-xl font-semibold mb-2">Processing Your Order</h3>
          <p className="text-center text-muted-foreground">We're generating your PayPal invoice. Please wait a moment...</p>
        </div>
      );
    } else if (checkoutState.status === 'success') {
      return (
        <div className="h-full flex flex-col items-center justify-center p-6">
          <div className="mb-6 text-[#27B376]">
            <CircleCheck className="h-16 w-16" />
          </div>
          <h3 className="text-xl font-semibold mb-2">Invoice Generated!</h3>
          <p className="text-center text-muted-foreground mb-6">
            We've sent a PayPal invoice to your email address. Please check your inbox to complete your purchase.
          </p>
          <p className="text-center text-amber-600 mb-6">
            <strong>Note:</strong> In test mode, invoices are created in draft status and need to be manually sent from your PayPal dashboard.
          </p>
          {checkoutState.paypalInvoiceUrl && (
            <div className="mb-6">
              <Button
                className="bg-primary hover:bg-primary/90 text-primary-foreground"
                asChild
              >
                <a href={checkoutState.paypalInvoiceUrl} target="_blank" rel="noopener noreferrer">
                  View Invoice in PayPal Dashboard
                </a>
              </Button>
            </div>
          )}
          <div className="text-center">
            <Button onClick={onBack}>
              Back to Store
            </Button>
          </div>
        </div>
      );
    } else if (checkoutState.status === 'error') {
      return (
        <div className="h-full flex flex-col items-center justify-center p-6">
          <div className="mb-6 text-destructive">
            <AlertCircle className="h-16 w-16" />
          </div>
          <h3 className="text-xl font-semibold mb-2">Something went wrong</h3>
          <p className="text-center text-muted-foreground mb-6">
            {checkoutState.error || "We couldn't generate your invoice. Please try again or contact support if the problem persists."}
          </p>
          <div className="text-center">
            <Button
              className="bg-primary hover:bg-primary/90 text-primary-foreground"
              onClick={handleTryAgain}
            >
              Try Again
            </Button>
          </div>
        </div>
      );
    }

    return null;
  };

  return (
    <div className="container mx-auto max-w-3xl">
      <div className="flex items-center mb-6">
        <Button
          variant="ghost"
          className="flex items-center text-primary hover:text-primary/80 mr-4"
          onClick={onBack}
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          <span>Back to products</span>
        </Button>
        <h2 className="text-2xl font-bold">Checkout</h2>
      </div>

      <Card className="shadow-sm">
        <CardContent className="p-6">
          {renderContent()}
        </CardContent>
      </Card>
    </div>
  );
};

export default CheckoutForm;
