import {
  users,
  type User,
  type InsertUser,
  type Product,
  type InsertProduct,
  type Invoice,
  type InsertInvoice,
  type CustomCheckoutPage,
  type InsertCustomCheckoutPage,
  type AllowedEmail,
  type InsertAllowedEmail,
  type EmailTemplate,
  type InsertEmailTemplate,
  type PaypalButton,
  type InsertPaypalButton,
  type CustomInvoice,
  type InsertCustomInvoice
} from "@shared/schema";
import { createHash, randomBytes } from 'crypto';

// Device interface
interface Device {
  id: string;
  name: string;
  ip: string;
  userAgent: string;
  lastLogin: string;
  createdAt: string;
}

// Recovery code interface
interface RecoveryCode {
  code: string;
  used: boolean;
}

export interface IStorage {
  getUser(id: number): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  getUserByEmail(email: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  verifyUserCredentials(username: string, password: string): Promise<boolean>;
  saveResetToken(userId: number, token: string, expiry: Date): Promise<void>;
  validateResetToken(token: string): Promise<boolean>;
  getUserByResetToken(token: string): Promise<User | undefined>;
  updateUserPassword(userId: number, password: string): Promise<void>;
  clearResetToken(userId: number): Promise<void>;
  updateUsername(userId: number, username: string): Promise<void>;
  updateAutoLoginSettings(userId: number, rememberMe: boolean): Promise<void>;
  enableTwoFactor(userId: number, secret: string): Promise<void>;
  disableTwoFactor(userId: number): Promise<void>;
  verifyTwoFactorToken(userId: number, token: string): Promise<boolean>;

  // Recovery code methods
  generateRecoveryCodes(userId: number): Promise<string[]>;
  verifyRecoveryCode(userId: number, code: string): Promise<boolean>;

  // Device tracking methods
  addDevice(userId: number, deviceInfo: Omit<Device, 'id' | 'createdAt' | 'lastLogin'>): Promise<Device>;
  getDevices(userId: number): Promise<Device[]>;
  updateDeviceLastLogin(userId: number, deviceId: string): Promise<void>;
  removeDevice(userId: number, deviceId: string): Promise<boolean>;

  // Product methods
  getProducts(): Promise<Product[]>;
  getProduct(id: number): Promise<Product | undefined>;
  createProduct(product: InsertProduct): Promise<Product>;

  // Invoice methods
  createInvoice(invoice: InsertInvoice): Promise<Invoice>;
  getInvoice(id: number): Promise<Invoice | undefined>;
  getInvoices(): Promise<Invoice[]>;
  updateInvoice(id: number, update: Partial<InsertInvoice>): Promise<Invoice | undefined>;

  // Configuration methods
  getGeneralSettings(): Promise<any>;
  getEmailConfig(): Promise<any>;
  getPaymentConfig(): Promise<any>;

  // Custom Checkout Page methods
  createCustomCheckoutPage(page: InsertCustomCheckoutPage): Promise<CustomCheckoutPage>;
  getCustomCheckoutPage(id: number): Promise<CustomCheckoutPage | undefined>;
  getCustomCheckoutPageBySlug(slug: string): Promise<CustomCheckoutPage | undefined>;
  getCustomCheckoutPages(): Promise<CustomCheckoutPage[]>;
  updateCustomCheckoutPage(id: number, update: Partial<InsertCustomCheckoutPage>): Promise<CustomCheckoutPage | undefined>;
  incrementCustomCheckoutPageViews(id: number): Promise<void>;
  incrementCustomCheckoutPageConversions(id: number): Promise<void>;
  deleteCustomCheckoutPage(id: number): Promise<boolean>;

  // Allowed Email methods
  getAllowedEmails(): Promise<AllowedEmail[]>;
  getAllowedEmail(id: number): Promise<AllowedEmail | undefined>;
  getEmailByAddress(email: string): Promise<AllowedEmail | undefined>;
  isEmailAllowed(email: string): Promise<boolean>;
  createAllowedEmail(email: InsertAllowedEmail): Promise<AllowedEmail>;
  updateAllowedEmail(id: number, update: Partial<InsertAllowedEmail>): Promise<AllowedEmail | undefined>;
  updateOrCreateAllowedEmail(emailAddress: string, update: Partial<InsertAllowedEmail>): Promise<AllowedEmail>;
  deleteAllowedEmail(id: number): Promise<boolean>;
  bulkCreateAllowedEmails(emails: string[]): Promise<{ success: number; failed: number }>;

  // Email Template methods
  getEmailTemplates(): Promise<EmailTemplate[]>;
  getEmailTemplate(id: number): Promise<EmailTemplate | undefined>;
  createEmailTemplate(template: InsertEmailTemplate): Promise<EmailTemplate>;
  updateEmailTemplate(id: number, update: Partial<InsertEmailTemplate>): Promise<EmailTemplate | undefined>;
  deleteEmailTemplate(id: number): Promise<boolean>;

  // PayPal Button methods
  getPaypalButtons(): Promise<PaypalButton[]>;
  getPaypalButton(id: number): Promise<PaypalButton | undefined>;
  createPaypalButton(button: InsertPaypalButton): Promise<PaypalButton>;
  updatePaypalButton(id: number, update: Partial<InsertPaypalButton>): Promise<PaypalButton | undefined>;
  deletePaypalButton(id: number): Promise<boolean>;

  // Custom Invoice methods
  getCustomInvoices(): Promise<CustomInvoice[]>;
  getCustomInvoice(id: number): Promise<CustomInvoice | undefined>;
  getCustomInvoiceByNumber(invoiceNumber: string): Promise<CustomInvoice | undefined>;
  createCustomInvoice(invoice: InsertCustomInvoice): Promise<CustomInvoice>;
  updateCustomInvoice(id: number, update: Partial<InsertCustomInvoice>): Promise<CustomInvoice | undefined>;
  incrementCustomInvoiceViewCount(id: number): Promise<void>;
  markCustomInvoiceAsPaid(id: number): Promise<CustomInvoice | undefined>;
  deleteCustomInvoice(id: number): Promise<boolean>;

  // Contact Inquiry methods
  createContactInquiry?(inquiry: any): Promise<any>;
  getContactInquiries?(): Promise<any[]>;
  updateContactInquiry?(id: number, update: any): Promise<any>;
}

export class MemStorage implements IStorage {
  private users: Map<number, User>;
  private products: Map<number, Product>;
  private invoices: Map<number, Invoice>;
  private customCheckoutPages: Map<number, CustomCheckoutPage>;
  private allowedEmails: Map<number, AllowedEmail>;
  private emailTemplates: Map<number, EmailTemplate>;
  private paypalButtons: Map<number, PaypalButton>;
  private customInvoices: Map<number, CustomInvoice>;
  private contactInquiries: Map<number, any>;

  userCurrentId: number;
  productCurrentId: number;
  invoiceCurrentId: number;
  customCheckoutPageCurrentId: number;
  allowedEmailCurrentId: number;
  emailTemplateCurrentId: number;
  paypalButtonCurrentId: number;
  customInvoiceCurrentId: number;
  contactInquiryCurrentId: number;


  constructor() {
    this.users = new Map();
    this.products = new Map();
    this.invoices = new Map();
    this.customCheckoutPages = new Map();
    this.allowedEmails = new Map();
    this.emailTemplates = new Map();
    this.paypalButtons = new Map();
    this.customInvoices = new Map();
    this.contactInquiries = new Map();

    this.userCurrentId = 1;
    this.productCurrentId = 1;
    this.invoiceCurrentId = 1;
    this.customCheckoutPageCurrentId = 1;
    this.allowedEmailCurrentId = 1;
    this.emailTemplateCurrentId = 1;
    this.paypalButtonCurrentId = 1;
    this.customInvoiceCurrentId = 1;
    this.contactInquiryCurrentId = 1;


    // Initialize with some products
    this.initializeProducts();

    // Initialize with some PayPal buttons
    this.initializePaypalButtons();




  }

  async getUser(id: number): Promise<User | undefined> {
    return this.users.get(id);
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    return Array.from(this.users.values()).find(
      (user) => user.username === username,
    );
  }

  async getUserByEmail(email: string): Promise<User | undefined> {
    return Array.from(this.users.values()).find(
      (user) => user.email === email,
    );
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const id = this.userCurrentId++;

    // Hash the password if it's not already hashed
    let password = insertUser.password;
    if (!password.match(/^[0-9a-f]{64}$/i)) {
      password = createHash('sha256').update(password).digest('hex');
    }

    const user: User = {
      ...insertUser,
      id,
      password,
      rememberMe: insertUser.rememberMe || false,
      resetToken: undefined,
      resetTokenExpiry: undefined,
      twoFactorSecret: undefined,
      twoFactorEnabled: false,
      recoveryCodes: [],
      devices: []
    };

    this.users.set(id, user);
    return user;
  }

  async verifyUserCredentials(username: string, password: string): Promise<boolean> {
    const user = await this.getUserByUsername(username);
    if (!user) return false;

    // Hash the password for comparison
    const hashedPassword = createHash('sha256').update(password).digest('hex');
    return user.password === hashedPassword;
  }

  async saveResetToken(userId: number, token: string, expiry: Date): Promise<void> {
    const user = await this.getUser(userId);
    if (!user) return;

    user.resetToken = token;
    user.resetTokenExpiry = expiry.toISOString();
    this.users.set(userId, user);
  }

  async validateResetToken(token: string): Promise<boolean> {
    const user = await this.getUserByResetToken(token);
    if (!user) return false;

    // Check if token is expired
    if (new Date() > new Date(user.resetTokenExpiry!)) {
      return false;
    }

    return true;
  }

  async getUserByResetToken(token: string): Promise<User | undefined> {
    return Array.from(this.users.values()).find(
      (user) => user.resetToken === token,
    );
  }

  async updateUserPassword(userId: number, password: string): Promise<void> {
    const user = await this.getUser(userId);
    if (!user) return;

    user.password = password;
    this.users.set(userId, user);
  }

  async clearResetToken(userId: number): Promise<void> {
    const user = await this.getUser(userId);
    if (!user) return;

    user.resetToken = undefined;
    user.resetTokenExpiry = undefined;
    this.users.set(userId, user);
  }

  async updateUsername(userId: number, username: string): Promise<void> {
    const user = await this.getUser(userId);
    if (!user) return;

    user.username = username;
    this.users.set(userId, user);
  }

  async updateAutoLoginSettings(userId: number, rememberMe: boolean): Promise<void> {
    const user = await this.getUser(userId);
    if (!user) return;

    user.rememberMe = rememberMe;
    this.users.set(userId, user);
  }

  async enableTwoFactor(userId: number, secret: string): Promise<void> {
    const user = await this.getUser(userId);
    if (!user) return;

    user.twoFactorSecret = secret;
    user.twoFactorEnabled = true;
    this.users.set(userId, user);
  }

  async disableTwoFactor(userId: number): Promise<void> {
    const user = await this.getUser(userId);
    if (!user) return;

    user.twoFactorSecret = undefined;
    user.twoFactorEnabled = false;
    this.users.set(userId, user);
  }

  async verifyTwoFactorToken(userId: number, token: string): Promise<boolean> {
    const user = await this.getUser(userId);
    if (!user || !user.twoFactorEnabled || !user.twoFactorSecret) return false;

    // The actual verification is done in the auth routes using otplib
    // This method is just a placeholder for the interface
    return true;
  }

  // Recovery code methods
  async generateRecoveryCodes(userId: number): Promise<string[]> {
    const user = await this.getUser(userId);
    if (!user) return [];

    // Generate 10 recovery codes
    const codes: string[] = [];
    for (let i = 0; i < 10; i++) {
      // Generate a random 8-character code
      const code = randomBytes(4).toString('hex').toUpperCase();
      codes.push(code);

      // Add to user's recovery codes
      user.recoveryCodes.push({
        code,
        used: false
      });
    }

    this.users.set(userId, user);
    return codes;
  }

  async verifyRecoveryCode(userId: number, code: string): Promise<boolean> {
    const user = await this.getUser(userId);
    if (!user) return false;

    // Find the recovery code
    const recoveryCodeIndex = user.recoveryCodes.findIndex(rc => rc.code === code && !rc.used);
    if (recoveryCodeIndex === -1) return false;

    // Mark the code as used
    user.recoveryCodes[recoveryCodeIndex].used = true;
    this.users.set(userId, user);

    return true;
  }

  // Device tracking methods
  async addDevice(userId: number, deviceInfo: Omit<Device, 'id' | 'createdAt' | 'lastLogin'>): Promise<Device> {
    const user = await this.getUser(userId);
    if (!user) throw new Error('User not found');

    const now = new Date().toISOString();
    const device: Device = {
      ...deviceInfo,
      id: randomBytes(16).toString('hex'),
      createdAt: now,
      lastLogin: now
    };

    user.devices.push(device);
    this.users.set(userId, user);

    return device;
  }

  async getDevices(userId: number): Promise<Device[]> {
    const user = await this.getUser(userId);
    if (!user) return [];

    return user.devices;
  }

  async updateDeviceLastLogin(userId: number, deviceId: string): Promise<void> {
    const user = await this.getUser(userId);
    if (!user) return;

    const deviceIndex = user.devices.findIndex(d => d.id === deviceId);
    if (deviceIndex === -1) return;

    user.devices[deviceIndex].lastLogin = new Date().toISOString();
    this.users.set(userId, user);
  }

  async removeDevice(userId: number, deviceId: string): Promise<boolean> {
    const user = await this.getUser(userId);
    if (!user) return false;

    const initialLength = user.devices.length;
    user.devices = user.devices.filter(d => d.id !== deviceId);

    if (user.devices.length === initialLength) {
      return false; // No device was removed
    }

    this.users.set(userId, user);
    return true;
  }

  async getProducts(): Promise<Product[]> {
    return Array.from(this.products.values());
  }

  async getProduct(id: number): Promise<Product | undefined> {
    return this.products.get(id);
  }

  async createProduct(insertProduct: InsertProduct): Promise<Product> {
    const id = this.productCurrentId++;
    const product: Product = { ...insertProduct, id };
    this.products.set(id, product);
    return product;
  }

  async createInvoice(insertInvoice: InsertInvoice): Promise<Invoice> {
    const id = this.invoiceCurrentId++;
    console.log('Creating invoice with data:', insertInvoice);
    const invoice: Invoice = {
      ...insertInvoice,
      id,
      isTrialOrder: insertInvoice.isTrialOrder || false,
      hasUpgraded: insertInvoice.hasUpgraded || false,
      upgradedAt: insertInvoice.upgradedAt || null
    };
    this.invoices.set(id, invoice);
    return invoice;
  }

  async getInvoice(id: number): Promise<Invoice | undefined> {
    return this.invoices.get(id);
  }

  async getInvoices(): Promise<Invoice[]> {
    return Array.from(this.invoices.values());
  }

  async updateInvoice(id: number, update: Partial<InsertInvoice>): Promise<Invoice | undefined> {
    const invoice = this.invoices.get(id);
    if (!invoice) return undefined;

    const updatedInvoice = { ...invoice, ...update };
    this.invoices.set(id, updatedInvoice);
    return updatedInvoice;
  }

  // Custom Checkout Page methods
  async createCustomCheckoutPage(insertPage: InsertCustomCheckoutPage): Promise<CustomCheckoutPage> {
    const id = this.customCheckoutPageCurrentId++;
    const page: CustomCheckoutPage = {
      ...insertPage,
      id,
      views: 0,
      conversions: 0
    };
    this.customCheckoutPages.set(id, page);
    return page;
  }

  async getCustomCheckoutPage(id: number): Promise<CustomCheckoutPage | undefined> {
    return this.customCheckoutPages.get(id);
  }

  async getCustomCheckoutPageBySlug(slug: string): Promise<CustomCheckoutPage | undefined> {
    return Array.from(this.customCheckoutPages.values()).find(
      (page) => page.slug === slug
    );
  }

  async getCustomCheckoutPages(): Promise<CustomCheckoutPage[]> {
    return Array.from(this.customCheckoutPages.values());
  }

  async updateCustomCheckoutPage(id: number, update: Partial<InsertCustomCheckoutPage>): Promise<CustomCheckoutPage | undefined> {
    const page = this.customCheckoutPages.get(id);
    if (!page) return undefined;

    const updatedPage = { ...page, ...update };
    this.customCheckoutPages.set(id, updatedPage);
    return updatedPage;
  }

  async incrementCustomCheckoutPageViews(id: number): Promise<void> {
    const page = this.customCheckoutPages.get(id);
    if (!page) return;

    page.views += 1;
    this.customCheckoutPages.set(id, page);
  }

  async incrementCustomCheckoutPageConversions(id: number): Promise<void> {
    const page = this.customCheckoutPages.get(id);
    if (!page) return;

    page.conversions += 1;
    this.customCheckoutPages.set(id, page);
  }

  async deleteCustomCheckoutPage(id: number): Promise<boolean> {
    return this.customCheckoutPages.delete(id);
  }

  private initializeProducts() {
    const products: InsertProduct[] = [
      {
        name: "Dashboard Pro Template",
        description: "Modern admin dashboard template with 50+ components, dark/light mode, and responsive design. Perfect for productivity apps and SaaS platforms.",
        price: "89.99",
        imageUrl: "https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=60",
        active: true
      },
      {
        name: "Task Management UI Kit",
        description: "Complete UI kit for task management applications with 100+ screens, components, and interactive prototypes for Figma.",
        price: "59.99",
        imageUrl: "https://images.unsplash.com/photo-1611224923853-80b023f02d71?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=60",
        active: true
      },
      {
        name: "Mobile Productivity App Template",
        description: "React Native template for productivity apps with calendar, notes, tasks, and team collaboration features. iOS & Android ready.",
        price: "129.99",
        imageUrl: "https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=60",
        active: true
      },
      {
        name: "Design System Starter Kit",
        description: "Complete design system with 200+ components, design tokens, documentation, and code examples for React and Vue.js.",
        price: "149.99",
        imageUrl: "https://images.unsplash.com/photo-1561070791-2526d30994b5?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=60",
        active: true
      },
      {
        name: "Calendar & Scheduling Template",
        description: "Advanced calendar and scheduling template with booking system, time zones, recurring events, and team management features.",
        price: "79.99",
        imageUrl: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=60",
        active: true
      },
      {
        name: "Note-Taking App UI Kit",
        description: "Beautiful note-taking app interface with rich text editor, markdown support, tags, and collaborative features. Includes Figma files.",
        price: "49.99",
        imageUrl: "https://images.unsplash.com/photo-1586281380349-632531db7ed4?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=60",
        active: true
      }
    ];

    products.forEach(product => {
      this.createProduct(product);
    });

    // Initialize with some test allowed emails
    const testEmails = ["<EMAIL>", "<EMAIL>", "<EMAIL>"];
    testEmails.forEach(email => {
      this.createAllowedEmail({
        email,
        notes: "Test email",
        lastSubject: "Welcome to our service",
        smtpProvider: "smtp-1",
        lastUpdated: new Date().toISOString(),
        createdAt: new Date().toISOString()
      });
    });
  }

  // Allowed Email methods
  async getAllowedEmails(): Promise<AllowedEmail[]> {
    return Array.from(this.allowedEmails.values());
  }

  async getAllowedEmail(id: number): Promise<AllowedEmail | undefined> {
    return this.allowedEmails.get(id);
  }

  async getEmailByAddress(email: string): Promise<AllowedEmail | undefined> {
    // Case-insensitive check
    const normalizedEmail = email.toLowerCase();
    return Array.from(this.allowedEmails.values()).find(
      (allowedEmail) => allowedEmail.email.toLowerCase() === normalizedEmail
    );
  }

  async isEmailAllowed(email: string): Promise<boolean> {
    // Case-insensitive check
    const normalizedEmail = email.toLowerCase();
    return Array.from(this.allowedEmails.values()).some(
      (allowedEmail) => allowedEmail.email.toLowerCase() === normalizedEmail
    );
  }

  async createAllowedEmail(insertAllowedEmail: InsertAllowedEmail): Promise<AllowedEmail> {
    const id = this.allowedEmailCurrentId++;
    const allowedEmail: AllowedEmail = { ...insertAllowedEmail, id };
    this.allowedEmails.set(id, allowedEmail);
    return allowedEmail;
  }

  async updateAllowedEmail(id: number, update: Partial<InsertAllowedEmail>): Promise<AllowedEmail | undefined> {
    const email = this.allowedEmails.get(id);
    if (!email) {
      return undefined;
    }
    const updatedEmail: AllowedEmail = { ...email, ...update };
    this.allowedEmails.set(id, updatedEmail);
    return updatedEmail;
  }

  async updateOrCreateAllowedEmail(emailAddress: string, update: Partial<InsertAllowedEmail>): Promise<AllowedEmail> {
    // Find existing email (case-insensitive)
    const normalizedEmail = emailAddress.toLowerCase();
    const existingEmail = Array.from(this.allowedEmails.values()).find(
      (allowedEmail) => allowedEmail.email.toLowerCase() === normalizedEmail
    );

    if (existingEmail) {
      // Update existing email
      const updatedEmail: AllowedEmail = { ...existingEmail, ...update };
      this.allowedEmails.set(existingEmail.id, updatedEmail);
      return updatedEmail;
    } else {
      // Create new email
      return this.createAllowedEmail({
        email: emailAddress,
        notes: update.notes || '',
        lastSubject: update.lastSubject,
        smtpProvider: update.smtpProvider,
        lastUpdated: update.lastUpdated || new Date().toISOString(),
        createdAt: update.createdAt || new Date().toISOString()
      });
    }
  }

  async deleteAllowedEmail(id: number): Promise<boolean> {
    return this.allowedEmails.delete(id);
  }

  // Configuration methods
  async getGeneralSettings(): Promise<any> {
    // Return a mock general settings object
    return {
      siteName: "TemplateHub Pro",
      siteDescription: "Premium productivity app templates and UI/UX design systems",
      logoUrl: "",
      faviconUrl: "",
      primaryColor: "#6366f1",
      secondaryColor: "#4f46e5",
      footerText: "© 2024 TemplateHub Pro",
      enableCheckout: true,
      enableCustomCheckout: true,
      enableTestMode: true,
      defaultTestCustomer: {
        enabled: true,
        name: "Test Designer",
        email: "<EMAIL>"
      },
      emailDomainRestriction: {
        enabled: false,
        allowedDomains: "gmail.com, hotmail.com, yahoo.com"
      }
    };
  }

  async getEmailConfig(): Promise<any> {
    // Return a mock email config
    return {
      providers: [
        {
          id: 'smtp-1',
          name: 'Primary SMTP',
          active: true,
          isDefault: true,
          isBackup: false,
          credentials: {
            host: 'smtp-relay.brevo.com',
            port: '587',
            secure: false,
            auth: {
              user: '<EMAIL>',
              pass: '********'
            },
            fromEmail: '<EMAIL>',
            fromName: 'PayPal Invoicer'
          }
        }
      ]
    };
  }

  async getPaymentConfig(): Promise<any> {
    // Return a mock payment config
    return {
      providers: [
        {
          id: 'paypal',
          name: 'PayPal',
          active: true,
          config: {
            clientId: '********',
            clientSecret: '********',
            mode: 'sandbox',
            webhookId: '',
            paypalEmail: '<EMAIL>'
          }
        },
        {
          id: 'custom-link',
          name: 'Custom Payment Links',
          active: true,
          config: {
            links: [
              {
                id: 'link-1',
                name: 'Default Payment Link',
                paymentLink: 'https://example.com/pay',
                buttonText: 'Pay Now',
                successRedirectUrl: 'https://example.com/thank-you',
                active: true
              }
            ],
            rotationMethod: 'round-robin',
            lastUsedIndex: 0
          }
        }
      ]
    };
  }

  async bulkCreateAllowedEmails(emails: string[]): Promise<{ success: number; failed: number }> {
    let success = 0;
    let failed = 0;

    for (const email of emails) {
      // Skip empty emails
      if (!email.trim()) {
        failed++;
        continue;
      }

      // Check if email already exists (case-insensitive)
      const normalizedEmail = email.trim().toLowerCase();
      const exists = Array.from(this.allowedEmails.values()).some(
        (allowedEmail) => allowedEmail.email.toLowerCase() === normalizedEmail
      );

      if (exists) {
        failed++;
        continue;
      }

      // Create the allowed email
      await this.createAllowedEmail({
        email: email.trim(),
        notes: "Bulk imported",
        lastSubject: "",
        smtpProvider: "",
        lastUpdated: new Date().toISOString(),
        createdAt: new Date().toISOString()
      });

      success++;
    }

    return { success, failed };
  }

  // Email Template methods
  async getEmailTemplates(): Promise<EmailTemplate[]> {
    return Array.from(this.emailTemplates.values());
  }

  async getEmailTemplate(id: number): Promise<EmailTemplate | undefined> {
    return this.emailTemplates.get(id);
  }

  async createEmailTemplate(template: InsertEmailTemplate): Promise<EmailTemplate> {
    const id = this.emailTemplateCurrentId++;
    const emailTemplate: EmailTemplate = { ...template, id };
    this.emailTemplates.set(id, emailTemplate);
    return emailTemplate;
  }

  async updateEmailTemplate(id: number, update: Partial<InsertEmailTemplate>): Promise<EmailTemplate | undefined> {
    const template = this.emailTemplates.get(id);

    if (!template) {
      return undefined;
    }

    const updatedTemplate: EmailTemplate = { ...template, ...update };
    this.emailTemplates.set(id, updatedTemplate);
    return updatedTemplate;
  }

  async deleteEmailTemplate(id: number): Promise<boolean> {
    return this.emailTemplates.delete(id);
  }

  // PayPal Button methods
  async getPaypalButtons(): Promise<PaypalButton[]> {
    return Array.from(this.paypalButtons.values());
  }

  async getPaypalButton(id: number): Promise<PaypalButton | undefined> {
    return this.paypalButtons.get(id);
  }

  async createPaypalButton(button: InsertPaypalButton): Promise<PaypalButton> {
    const id = this.paypalButtonCurrentId++;
    const paypalButton: PaypalButton = { ...button, id };
    this.paypalButtons.set(id, paypalButton);
    return paypalButton;
  }

  async updatePaypalButton(id: number, update: Partial<InsertPaypalButton>): Promise<PaypalButton | undefined> {
    const button = this.paypalButtons.get(id);
    if (!button) return undefined;

    const updatedButton = { ...button, ...update };
    this.paypalButtons.set(id, updatedButton);
    return updatedButton;
  }

  async deletePaypalButton(id: number): Promise<boolean> {
    return this.paypalButtons.delete(id);
  }

  // Custom Invoice methods
  async getCustomInvoices(): Promise<CustomInvoice[]> {
    return Array.from(this.customInvoices.values());
  }

  async getCustomInvoice(id: number): Promise<CustomInvoice | undefined> {
    return this.customInvoices.get(id);
  }

  async getCustomInvoiceByNumber(invoiceNumber: string): Promise<CustomInvoice | undefined> {
    return Array.from(this.customInvoices.values()).find(
      (invoice) => invoice.invoiceNumber === invoiceNumber
    );
  }

  async createCustomInvoice(invoice: InsertCustomInvoice): Promise<CustomInvoice> {
    const id = this.customInvoiceCurrentId++;
    const customInvoice: CustomInvoice = {
      ...invoice,
      id,
      viewCount: 0,
      paidAt: null
    };
    this.customInvoices.set(id, customInvoice);
    return customInvoice;
  }

  async updateCustomInvoice(id: number, update: Partial<InsertCustomInvoice>): Promise<CustomInvoice | undefined> {
    const invoice = this.customInvoices.get(id);
    if (!invoice) return undefined;

    const updatedInvoice = { ...invoice, ...update };
    this.customInvoices.set(id, updatedInvoice);
    return updatedInvoice;
  }

  async incrementCustomInvoiceViewCount(id: number): Promise<void> {
    const invoice = this.customInvoices.get(id);
    if (!invoice) return;

    invoice.viewCount += 1;
    this.customInvoices.set(id, invoice);
  }

  async markCustomInvoiceAsPaid(id: number): Promise<CustomInvoice | undefined> {
    const invoice = this.customInvoices.get(id);
    if (!invoice) return undefined;

    const updatedInvoice = {
      ...invoice,
      status: 'paid',
      paidAt: new Date().toISOString()
    };
    this.customInvoices.set(id, updatedInvoice);
    return updatedInvoice;
  }

  async deleteCustomInvoice(id: number): Promise<boolean> {
    return this.customInvoices.delete(id);
  }







  // Initialize sample PayPal buttons
  private initializePaypalButtons() {
    const sampleButtons: InsertPaypalButton[] = [
      {
        name: "Basic PayPal Button",
        buttonCode: `<form action="https://www.paypal.com/cgi-bin/webscr" method="post" target="_top">
<input type="hidden" name="cmd" value="_s-xclick">
<input type="hidden" name="hosted_button_id" value="SAMPLE123456">
<input type="image" src="https://www.paypalobjects.com/en_US/i/btn/btn_buynowCC_LG.gif" border="0" name="submit" alt="PayPal - The safer, easier way to pay online!">
<img alt="" border="0" src="https://www.paypalobjects.com/en_US/i/scr/pixel.gif" width="1" height="1">
</form>`,
        description: "Standard PayPal Buy Now button",
        active: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        name: "Premium PayPal Button",
        buttonCode: `<form action="https://www.paypal.com/cgi-bin/webscr" method="post" target="_top">
<input type="hidden" name="cmd" value="_s-xclick">
<input type="hidden" name="hosted_button_id" value="PREMIUM789012">
<input type="image" src="https://www.paypalobjects.com/en_US/i/btn/btn_paynowCC_LG.gif" border="0" name="submit" alt="PayPal - The safer, easier way to pay online!">
<img alt="" border="0" src="https://www.paypalobjects.com/en_US/i/scr/pixel.gif" width="1" height="1">
</form>`,
        description: "Premium PayPal Pay Now button",
        active: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        name: "Subscription PayPal Button",
        buttonCode: `<form action="https://www.paypal.com/cgi-bin/webscr" method="post" target="_top">
<input type="hidden" name="cmd" value="_s-xclick">
<input type="hidden" name="hosted_button_id" value="SUB345678">
<input type="image" src="https://www.paypalobjects.com/en_US/i/btn/btn_subscribeCC_LG.gif" border="0" name="submit" alt="PayPal - The safer, easier way to pay online!">
<img alt="" border="0" src="https://www.paypalobjects.com/en_US/i/scr/pixel.gif" width="1" height="1">
</form>`,
        description: "PayPal Subscription button",
        active: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ];

    sampleButtons.forEach(button => {
      this.createPaypalButton(button);
    });
  }

  // Contact Inquiry methods
  async createContactInquiry(inquiry: any): Promise<any> {
    const id = this.contactInquiryCurrentId++;
    const contactInquiry = {
      ...inquiry,
      id
    };
    this.contactInquiries.set(id, contactInquiry);
    return contactInquiry;
  }

  async getContactInquiries(): Promise<any[]> {
    return Array.from(this.contactInquiries.values()).sort((a, b) =>
      new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    );
  }

  async updateContactInquiry(id: number, update: any): Promise<any> {
    const inquiry = this.contactInquiries.get(id);
    if (!inquiry) return undefined;

    const updatedInquiry = { ...inquiry, ...update };
    this.contactInquiries.set(id, updatedInquiry);
    return updatedInquiry;
  }
}

export const storage = new MemStorage();
