import { Router, Request, Response } from 'express';
import { z } from 'zod';
import { storage } from '../storage';
import { sendEmail } from '../services/email';

export const contactRouter = Router();

// Contact form schema
const contactFormSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  email: z.string().email('Please enter a valid email address'),
  subject: z.string().min(5, 'Subject must be at least 5 characters'),
  message: z.string().min(10, 'Message must be at least 10 characters'),
  checkoutPageSlug: z.string().optional(),
});

// Submit contact form
contactRouter.post('/', async (req: Request, res: Response) => {
  try {
    const validatedData = contactFormSchema.parse(req.body);

    // Create a contact inquiry record
    const contactInquiry = {
      id: Date.now(), // Simple ID generation
      name: validatedData.name,
      email: validatedData.email,
      subject: validatedData.subject,
      message: validatedData.message,
      checkoutPageSlug: validatedData.checkoutPageSlug,
      status: 'new',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    // Store the contact inquiry
    if (storage.createContactInquiry) {
      await storage.createContactInquiry(contactInquiry);
    }

    // Get checkout page settings for SMTP configuration
    let checkoutPage = null;
    if (validatedData.checkoutPageSlug) {
      const allPages = await storage.getCustomCheckoutPages();
      checkoutPage = allPages.find(page => page.slug === validatedData.checkoutPageSlug);
    }

    // Send email notification to admin using the checkout page's SMTP settings
    try {
      const emailContent = `
New Contact Inquiry Received

From: ${validatedData.name}
Email: ${validatedData.email}
Subject: ${validatedData.subject}

Message:
${validatedData.message}

${validatedData.checkoutPageSlug ? `Checkout Page: ${validatedData.checkoutPageSlug}` : ''}

---
This inquiry was submitted through the contact form and has been saved to your admin panel.
You can view and manage it at: ${req.protocol}://${req.get('host')}/admin/contact-inquiries
      `.trim();

      // Use checkout page's SMTP settings if available, otherwise use default
      const smtpSettings = checkoutPage?.smtpSettings || null;

      await sendEmail({
        to: checkoutPage?.supportEmail || '<EMAIL>', // Use checkout page support email or default
        subject: `New Contact Inquiry: ${validatedData.subject}`,
        text: emailContent,
        html: emailContent.replace(/\n/g, '<br>'),
      }, smtpSettings);

      console.log('📧 Contact inquiry email sent successfully');
    } catch (emailError) {
      console.error('Failed to send contact inquiry email:', emailError);
      // Don't fail the request if email sending fails
    }

    // Log the contact inquiry for debugging
    console.log('📧 New contact inquiry received:');
    console.log(`From: ${validatedData.name} (${validatedData.email})`);
    console.log(`Subject: ${validatedData.subject}`);
    console.log(`Checkout Page: ${validatedData.checkoutPageSlug || 'N/A'}`);
    console.log(`Message: ${validatedData.message}`);
    console.log('---');

    res.json({
      success: true,
      message: 'Your message has been sent successfully. We will get back to you soon.'
    });

  } catch (error) {
    console.error('Contact form submission error:', error);

    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        errors: error.errors
      });
    }

    res.status(500).json({
      success: false,
      message: 'Failed to send message. Please try again later.'
    });
  }
});

// Get contact inquiries (admin only)
contactRouter.get('/', async (req: Request, res: Response) => {
  try {
    // Simple admin check
    if (!req.session?.isAdmin) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    // Get all contact inquiries
    const inquiries = storage.getContactInquiries ? await storage.getContactInquiries() : [];

    res.json(inquiries);
  } catch (error) {
    console.error('Error fetching contact inquiries:', error);
    res.status(500).json({ message: 'Failed to fetch contact inquiries' });
  }
});

// Update contact inquiry status (admin only)
contactRouter.put('/:id', async (req: Request, res: Response) => {
  try {
    if (!req.session?.isAdmin) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    const id = parseInt(req.params.id);
    const { status, notes } = req.body;

    if (storage.updateContactInquiry) {
      const updatedInquiry = await storage.updateContactInquiry(id, {
        status,
        notes,
        updatedAt: new Date().toISOString()
      });

      if (!updatedInquiry) {
        return res.status(404).json({ message: 'Contact inquiry not found' });
      }

      res.json(updatedInquiry);
    } else {
      res.status(404).json({ message: 'Contact inquiry not found' });
    }
  } catch (error) {
    console.error('Error updating contact inquiry:', error);
    res.status(500).json({ message: 'Failed to update contact inquiry' });
  }
});
