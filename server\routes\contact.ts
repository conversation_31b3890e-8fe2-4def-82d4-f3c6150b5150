import { Router, Request, Response } from 'express';
import { z } from 'zod';
import { storage } from '../storage';

export const contactRouter = Router();

// Contact form schema
const contactFormSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  email: z.string().email('Please enter a valid email address'),
  subject: z.string().min(5, 'Subject must be at least 5 characters'),
  message: z.string().min(10, 'Message must be at least 10 characters'),
});

// Submit contact form
contactRouter.post('/', async (req: Request, res: Response) => {
  try {
    const validatedData = contactFormSchema.parse(req.body);
    
    // Create a contact inquiry record
    const contactInquiry = {
      id: Date.now(), // Simple ID generation
      name: validatedData.name,
      email: validatedData.email,
      subject: validatedData.subject,
      message: validatedData.message,
      status: 'new',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    // Store the contact inquiry (you can extend storage to handle this)
    await storage.createContactInquiry?.(contactInquiry) || 
          console.log('Contact inquiry received:', contactInquiry);

    // Here you could also send an email notification to admin
    // For now, we'll just log it and return success
    console.log('📧 New contact inquiry received:');
    console.log(`From: ${validatedData.name} (${validatedData.email})`);
    console.log(`Subject: ${validatedData.subject}`);
    console.log(`Message: ${validatedData.message}`);
    console.log('---');

    // You could integrate with your email service here
    // await sendEmailToAdmin({
    //   subject: `New Contact Inquiry: ${validatedData.subject}`,
    //   body: `
    //     Name: ${validatedData.name}
    //     Email: ${validatedData.email}
    //     Subject: ${validatedData.subject}
    //     
    //     Message:
    //     ${validatedData.message}
    //   `
    // });

    res.json({ 
      success: true, 
      message: 'Your message has been sent successfully. We will get back to you soon.' 
    });

  } catch (error) {
    console.error('Contact form submission error:', error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        errors: error.errors
      });
    }

    res.status(500).json({ 
      success: false, 
      message: 'Failed to send message. Please try again later.' 
    });
  }
});

// Get contact inquiries (admin only)
contactRouter.get('/', async (req: Request, res: Response) => {
  try {
    // Simple admin check - you might want to use your existing auth middleware
    if (!req.session?.isAdmin) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    // Get all contact inquiries
    const inquiries = await storage.getContactInquiries?.() || [];
    
    res.json(inquiries);
  } catch (error) {
    console.error('Error fetching contact inquiries:', error);
    res.status(500).json({ message: 'Failed to fetch contact inquiries' });
  }
});

// Update contact inquiry status (admin only)
contactRouter.put('/:id', async (req: Request, res: Response) => {
  try {
    if (!req.session?.isAdmin) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    const id = parseInt(req.params.id);
    const { status, notes } = req.body;

    const updatedInquiry = await storage.updateContactInquiry?.(id, {
      status,
      notes,
      updatedAt: new Date().toISOString()
    });

    if (!updatedInquiry) {
      return res.status(404).json({ message: 'Contact inquiry not found' });
    }

    res.json(updatedInquiry);
  } catch (error) {
    console.error('Error updating contact inquiry:', error);
    res.status(500).json({ message: 'Failed to update contact inquiry' });
  }
});
