import { storage } from './server/storage.ts';

async function fixCheckoutPages() {
  try {
    console.log('Fixing checkout pages...');

    // Get all checkout pages
    const pages = await storage.getCustomCheckoutPages();
    console.log(`Found ${pages.length} checkout pages`);

    for (const page of pages) {
      console.log(`\nCurrent page: ${page.title} (ID: ${page.id})`);
      console.log(`  - requireAllowedEmail: ${page.requireAllowedEmail}`);
      console.log(`  - requireUsername: ${page.requireUsername}`);

      // Update the page to set requireAllowedEmail to false
      const updatedPage = await storage.updateCustomCheckoutPage(page.id, {
        ...page,
        requireAllowedEmail: false,
        requireUsername: false
      });

      console.log(`✅ Updated page: ${page.title}`);
      console.log(`  - New requireAllowedEmail: ${updatedPage.requireAllowedEmail}`);
      console.log(`  - New requireUsername: ${updatedPage.requireUsername}`);
    }

    console.log('\n🎉 All checkout pages fixed!');
    process.exit(0);
  } catch (error) {
    console.error('Error fixing checkout pages:', error);
    process.exit(1);
  }
}

fixCheckoutPages();
