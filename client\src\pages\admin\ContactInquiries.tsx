import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { useToast } from '@/hooks/use-toast';
import { Mail, MessageSquare, Clock, CheckCircle, XCircle, AlertCircle, Eye } from 'lucide-react';

interface ContactInquiry {
  id: number;
  name: string;
  email: string;
  subject: string;
  message: string;
  status: 'new' | 'in_progress' | 'resolved' | 'closed';
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

const statusConfig = {
  new: { label: 'New', color: 'bg-blue-500', icon: AlertCircle },
  in_progress: { label: 'In Progress', color: 'bg-yellow-500', icon: Clock },
  resolved: { label: 'Resolved', color: 'bg-green-500', icon: CheckCircle },
  closed: { label: 'Closed', color: 'bg-gray-500', icon: XCircle },
};

export default function ContactInquiries() {
  const [selectedInquiry, setSelectedInquiry] = useState<ContactInquiry | null>(null);
  const [notes, setNotes] = useState('');
  const [status, setStatus] = useState<string>('');
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch contact inquiries
  const { data: inquiries = [], isLoading, error } = useQuery({
    queryKey: ['/api/contact'],
    queryFn: () => apiRequest('/api/contact', 'GET'),
  });

  // Update inquiry mutation
  const updateInquiryMutation = useMutation({
    mutationFn: async ({ id, updates }: { id: number; updates: any }) => {
      return apiRequest(`/api/contact/${id}`, 'PUT', updates);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/contact'] });
      toast({
        title: "Success",
        description: "Contact inquiry updated successfully.",
      });
      setSelectedInquiry(null);
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to update contact inquiry.",
        variant: "destructive",
      });
    },
  });

  const handleViewInquiry = (inquiry: ContactInquiry) => {
    setSelectedInquiry(inquiry);
    setNotes(inquiry.notes || '');
    setStatus(inquiry.status);
  };

  const handleUpdateInquiry = () => {
    if (!selectedInquiry) return;

    updateInquiryMutation.mutate({
      id: selectedInquiry.id,
      updates: {
        status,
        notes,
      },
    });
  };

  const getStatusBadge = (status: string) => {
    const config = statusConfig[status as keyof typeof statusConfig];
    if (!config) return null;

    const Icon = config.icon;
    return (
      <Badge className={`${config.color} text-white`}>
        <Icon className="h-3 w-3 mr-1" />
        {config.label}
      </Badge>
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold">Contact Inquiries</h1>
          <p className="text-muted-foreground">Manage customer contact inquiries</p>
        </div>
        <div className="grid gap-4">
          {[1, 2, 3].map((i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-4 bg-gray-200 rounded w-1/4 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2 mb-4"></div>
                <div className="h-3 bg-gray-200 rounded w-3/4"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold">Contact Inquiries</h1>
          <p className="text-muted-foreground">Manage customer contact inquiries</p>
        </div>
        <Card>
          <CardContent className="p-6 text-center">
            <XCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">Error Loading Inquiries</h3>
            <p className="text-muted-foreground">
              {error instanceof Error ? error.message : 'Failed to load contact inquiries'}
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold">Contact Inquiries</h1>
        <p className="text-muted-foreground">Manage customer contact inquiries and support requests</p>
      </div>

      {/* Stats */}
      <div className="grid gap-4 md:grid-cols-4">
        {Object.entries(statusConfig).map(([statusKey, config]) => {
          const count = inquiries.filter((inquiry: ContactInquiry) => inquiry.status === statusKey).length;
          const Icon = config.icon;
          return (
            <Card key={statusKey}>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <Icon className={`h-4 w-4 text-white`} />
                  <div>
                    <p className="text-sm font-medium">{config.label}</p>
                    <p className="text-2xl font-bold">{count}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Inquiries List */}
      <div className="space-y-4">
        {inquiries.length === 0 ? (
          <Card>
            <CardContent className="p-6 text-center">
              <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No Contact Inquiries</h3>
              <p className="text-muted-foreground">
                No contact inquiries have been submitted yet.
              </p>
            </CardContent>
          </Card>
        ) : (
          inquiries.map((inquiry: ContactInquiry) => (
            <Card key={inquiry.id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <h3 className="font-semibold">{inquiry.subject}</h3>
                      {getStatusBadge(inquiry.status)}
                    </div>
                    <div className="flex items-center space-x-4 text-sm text-muted-foreground mb-3">
                      <div className="flex items-center space-x-1">
                        <Mail className="h-4 w-4" />
                        <span>{inquiry.name} ({inquiry.email})</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Clock className="h-4 w-4" />
                        <span>{formatDate(inquiry.createdAt)}</span>
                      </div>
                    </div>
                    <p className="text-sm text-gray-600 line-clamp-2">
                      {inquiry.message}
                    </p>
                  </div>
                  <Dialog>
                    <DialogTrigger asChild>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleViewInquiry(inquiry)}
                      >
                        <Eye className="h-4 w-4 mr-2" />
                        View
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="max-w-2xl">
                      <DialogHeader>
                        <DialogTitle>Contact Inquiry Details</DialogTitle>
                        <DialogDescription>
                          View and manage this contact inquiry
                        </DialogDescription>
                      </DialogHeader>
                      {selectedInquiry && (
                        <div className="space-y-4">
                          <div className="grid grid-cols-2 gap-4">
                            <div>
                              <Label className="text-sm font-medium">Name</Label>
                              <p className="text-sm">{selectedInquiry.name}</p>
                            </div>
                            <div>
                              <Label className="text-sm font-medium">Email</Label>
                              <p className="text-sm">{selectedInquiry.email}</p>
                            </div>
                          </div>
                          <div>
                            <Label className="text-sm font-medium">Subject</Label>
                            <p className="text-sm">{selectedInquiry.subject}</p>
                          </div>
                          <div>
                            <Label className="text-sm font-medium">Message</Label>
                            <p className="text-sm whitespace-pre-wrap bg-gray-50 p-3 rounded">
                              {selectedInquiry.message}
                            </p>
                          </div>
                          <div className="grid grid-cols-2 gap-4">
                            <div>
                              <Label className="text-sm font-medium">Created</Label>
                              <p className="text-sm">{formatDate(selectedInquiry.createdAt)}</p>
                            </div>
                            <div>
                              <Label className="text-sm font-medium">Last Updated</Label>
                              <p className="text-sm">{formatDate(selectedInquiry.updatedAt)}</p>
                            </div>
                          </div>
                          <div>
                            <Label htmlFor="status">Status</Label>
                            <Select value={status} onValueChange={setStatus}>
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                {Object.entries(statusConfig).map(([key, config]) => (
                                  <SelectItem key={key} value={key}>
                                    {config.label}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>
                          <div>
                            <Label htmlFor="notes">Admin Notes</Label>
                            <Textarea
                              id="notes"
                              value={notes}
                              onChange={(e) => setNotes(e.target.value)}
                              placeholder="Add internal notes about this inquiry..."
                              rows={3}
                            />
                          </div>
                          <div className="flex justify-end space-x-2">
                            <Button
                              onClick={handleUpdateInquiry}
                              disabled={updateInquiryMutation.isPending}
                            >
                              {updateInquiryMutation.isPending ? 'Updating...' : 'Update Inquiry'}
                            </Button>
                          </div>
                        </div>
                      )}
                    </DialogContent>
                  </Dialog>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </div>
  );
}
